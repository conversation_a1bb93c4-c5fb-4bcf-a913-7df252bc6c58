{"expo": {"name": "NetOne Attendance", "slug": "netone-attendance", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "netoneattendance", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.emacliam.netoneattendance", "infoPlist": {"NSCameraUsageDescription": "This app needs access to camera to scan QR codes for attendance tracking.", "NSLocationWhenInUseUsageDescription": "This app needs access to location to record attendance location for check-in and check-out."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.emacliam.netoneattendance", "permissions": ["CAMERA", "ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], ["expo-camera", {"cameraPermission": "Allow $(PRODUCT_NAME) to access your camera to scan QR codes for attendance tracking."}], ["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow $(PRODUCT_NAME) to use your location to record attendance location."}]], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "09c9377c-9394-4c5e-b0f0-42edbdf951c5"}}}}