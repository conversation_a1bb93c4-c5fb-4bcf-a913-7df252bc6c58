import AsyncStorage from "@react-native-async-storage/async-storage";
import { BlurView } from "expo-blur";
import { useRouter } from "expo-router";
import { Eye, EyeOff, Lock, Mail } from "lucide-react-native";
import React, { useState } from "react";
import {
  Alert,
  Image,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  useColorScheme,
  View,
} from "react-native";
import DeviceInfo from "react-native-device-info";
import CheckInButton from "../../components/CheckInButton";
import { BASE_URL } from "../../utils/config";

export default function LoginScreen() {
  const colorScheme = useColorScheme() || "light";
  const textColor = colorScheme === "dark" ? "white" : "black";
  const [ecNumber, setEcNumber] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [ecNumberError, setEcNumberError] = useState("");
  const [passwordError, setPasswordError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const validateEcNumber = (ecNumber: string) => {
    const regex = /^\d{4,8}$/;
    return regex.test(ecNumber);
  };

  const handleLogin = async () => {
    setEcNumberError("");
    setPasswordError("");

    let isValid = true;

    if (!ecNumber) {
      setEcNumberError("Employee Code is required");
      isValid = false;
    } else if (!validateEcNumber(ecNumber)) {
      setEcNumberError("Please enter a valid employee code (4-8 digits)");
      isValid = false;
    }

    if (!password) {
      setPasswordError("Password is required");
      isValid = false;
    }

    if (!isValid) return;

    setIsLoading(true);

    try {
      // Get device ID with ecNumber
      const baseDeviceId = await DeviceInfo.getDeviceId();
      const combinedDeviceId = `${baseDeviceId}-${ecNumber}`;
      console.log(ecNumber, password, combinedDeviceId);

      const response = await fetch(`${BASE_URL}/api/auth/register`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ecNumber,
          password,
          deviceId: combinedDeviceId,
          isAdmin: false,
          fullName: "",
        }),
      });

      const data = await response.json();

      if (data.success) {
        await AsyncStorage.setItem("token", data.body);
        await AsyncStorage.setItem("ecNumber", ecNumber);
        router.replace("/(tabs)");
      } else {
        Alert.alert(
          "Login Failed",
          data.message || "Invalid employee code or password."
        );
      }
    } catch (error) {
      console.error(error);
      Alert.alert("Login Error", "An error occurred during login.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === "ios" ? "padding" : "height"}>
      <View style={styles.backgroundContainer}>
        <BlurView
          intensity={20}
          tint="default"
          style={StyleSheet.absoluteFill}
        />
        <View style={[styles.orangeCircle, styles.topLeftCircle]} />
        <View style={[styles.orangeCircle, styles.bottomRightCircle]} />
        <View style={[styles.orangeCircle, styles.centerTopCircle]} />
        <View style={[styles.orangeCircle, styles.centerBottomCircle]} />
        <View style={[styles.orangeCircle, styles.rightCenterCircle]} />
        <BlurView
          intensity={80}
          tint="light"
          style={[StyleSheet.absoluteFill, { zIndex: 1 }]}
        />
      </View>

      <ScrollView
        contentContainerStyle={[
          styles.container,
          { backgroundColor: "transparent" },
        ]}>
        <View style={styles.logoContainer}>
          <Image
            source={require("../../assets/images/logo.png")}
            style={styles.logo}
            resizeMode="contain"
          />
          <Text style={[styles.appName, { color: textColor }]}>
            NetOne Attendance
          </Text>
          <Text style={[styles.tagline, { color: textColor }]}>
            Sign in with your employee code
          </Text>
        </View>

        <View style={styles.formContainer}>
          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: textColor }]}>
              Employee Code
            </Text>
            <View
              style={[
                styles.inputContainer,
                { borderColor: ecNumberError ? "red" : "#ccc" },
              ]}>
              <Mail size={20} color="#888" style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder="157638"
                placeholderTextColor="#A9A9A9"
                keyboardType="numeric"
                autoCapitalize="none"
                value={ecNumber}
                onChangeText={(text) => {
                  setEcNumber(text);
                  setEcNumberError("");
                }}
              />
            </View>
            {ecNumberError ? (
              <Text style={styles.errorText}>{ecNumberError}</Text>
            ) : null}
          </View>

          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: textColor }]}>Password</Text>
            <View
              style={[
                styles.inputContainer,
                { borderColor: passwordError ? "red" : "#ccc" },
              ]}>
              <Lock size={20} color="#888" style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder="Your password"
                placeholderTextColor="#A9A9A9"
                secureTextEntry={!showPassword}
                value={password}
                onChangeText={(text) => {
                  setPassword(text);
                  setPasswordError("");
                }}
              />
              <TouchableOpacity
                style={styles.visibilityToggle}
                onPress={() => setShowPassword(!showPassword)}>
                {showPassword ? (
                  <EyeOff size={20} color="#888" />
                ) : (
                  <Eye size={20} color="#888" />
                )}
              </TouchableOpacity>
            </View>
            {passwordError ? (
              <Text style={styles.errorText}>{passwordError}</Text>
            ) : null}
          </View>

          <CheckInButton
            title="Sign In"
            onPress={handleLogin}
            isLoading={isLoading}
            style={styles.loginButton}
          />

          <View style={styles.helpContainer}>
            <Text style={[styles.helpText, { color: textColor }]}>
              Don&apos;t have an account?
            </Text>
            <TouchableOpacity onPress={() => router.push("/(auth)/register")}>
              <Text style={styles.contactText}>Register</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  backgroundContainer: {
    position: "absolute",
    width: "100%",
    height: "100%",
    zIndex: -1,
  },
  orangeCircle: {
    position: "absolute",
    backgroundColor: "rgba(255, 140, 0, 0.5)",
  },
  topLeftCircle: {
    top: -80,
    left: -60,
    width: 200,
    height: 200,
    borderRadius: 100,
  },
  bottomRightCircle: {
    bottom: -70,
    right: -40,
    width: 220,
    height: 220,
    borderRadius: 110,
    backgroundColor: "rgba(255, 160, 0, 0.60)",
  },
  centerTopCircle: {
    top: "15%",
    right: "20%",
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: "rgba(213, 163, 138, 0.5)",
  },
  centerBottomCircle: {
    bottom: "25%",
    left: "10%",
    width: 150,
    height: 150,
    borderRadius: 75,
  },
  rightCenterCircle: {
    top: "45%",
    right: -30,
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  container: {
    flexGrow: 1,
    justifyContent: "center",
    padding: 24,
  },
  logoContainer: {
    alignItems: "center",
    marginBottom: 40,
  },
  logo: {
    width: 100,
    height: 100,
    marginBottom: 16,
  },
  appName: {
    fontSize: 28,
    fontWeight: "bold",
    marginBottom: 8,
  },
  tagline: {
    fontSize: 16,
  },
  formContainer: {
    width: "100%",
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    fontWeight: "500",
  },
  inputContainer: {
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    backgroundColor: "rgba(255, 255, 255, 0.8)",
  },
  inputIcon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    fontSize: 16,
    paddingVertical: 12,
    color: "#000",
  },
  visibilityToggle: {
    padding: 4,
  },
  errorText: {
    fontSize: 14,
    marginTop: 4,
    color: "red",
  },
  loginButton: {
    marginBottom: 24,
    height: 50,
  },
  helpContainer: {
    flexDirection: "row",
    justifyContent: "center",
    gap: 4,
    marginTop: 16,
  },
  helpText: {
    fontSize: 14,
  },
  contactText: {
    fontSize: 14,
    color: "#3A86FF",
    fontWeight: "bold",
  },
});
