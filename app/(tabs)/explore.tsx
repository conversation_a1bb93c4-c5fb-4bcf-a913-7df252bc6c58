import AsyncStorage from "@react-native-async-storage/async-storage";
import { Calendar, Clock, Filter, MapPin } from "lucide-react-native";
import React, { useCallback, useEffect, useState } from "react";
import {
  ActivityIndicator,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  useColorScheme,
  View,
} from "react-native";
import { Colors } from "../../constants/Colors";
import {
  AttendanceRecord,
  getAttendanceLogs,
} from "../../services/attendanceService";

export default function ActivityLogScreen() {
  const colorScheme = useColorScheme() || "light";
  const colors = Colors?.[colorScheme] ||
    Colors?.light || {
      text: "#000",
      background: "#fff",
      primary: "#3A86FF",
      secondary: "#FF732",
      success: "#10B981",
      warning: "#F59E0B",
      error: "#EF4444",
      card: "#FFFFFF",
      border: "#E5E7EB",
      placeholder: "#9CA3AF",
    };

  const [user, setUser] = useState({ ecNumber: "", name: "User" });
  const [records, setRecords] = useState<AttendanceRecord[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [filter, setFilter] = useState<"all" | "checkin" | "checkout">("all");

  // Load user data from AsyncStorage
  useEffect(() => {
    const loadUserData = async () => {
      try {
        const storedEcNumber = await AsyncStorage.getItem("ecNumber");
        if (storedEcNumber) {
          setUser((prev) => ({ ...prev, ecNumber: storedEcNumber }));
        }
      } catch (error) {
        console.error("Failed to load user data:", error);
      }
    };

    loadUserData();
  }, []);

  // Load attendance records
  const loadRecords = useCallback(async () => {
    if (user.ecNumber) {
      try {
        setIsLoading(true);
        const logs = await getAttendanceLogs(user.ecNumber);
        setRecords(logs || []);
      } catch (error) {
        console.error("Failed to load attendance records", error);
        setRecords([]);
      } finally {
        setIsLoading(false);
      }
    }
  }, [user.ecNumber]);

  useEffect(() => {
    loadRecords();
  }, [loadRecords]);

  // Apply filter to records
  const filteredRecords = records.filter((record) => {
    if (filter === "all") return true;
    if (filter === "checkin") return !record.checkOutTime;
    if (filter === "checkout") return !!record.checkOutTime;
    return true;
  });

  // Group records by date
  const groupedRecords: { [date: string]: AttendanceRecord[] } = {};

  filteredRecords.forEach((record) => {
    const date = new Date(record.checkInDate).toLocaleDateString("en-US", {
      month: "long",
      day: "numeric",
      year: "numeric",
    });

    if (!groupedRecords[date]) {
      groupedRecords[date] = [];
    }

    groupedRecords[date].push(record);
  });

  // Sort dates in descending order (most recent first)
  const sortedDates = Object.keys(groupedRecords).sort((a, b) => {
    return new Date(b).getTime() - new Date(a).getTime();
  });

  // Format time from timestamp string
  const formatTime = (timeString: string) => {
    try {
      if (!timeString) return "N/A";
      const timePart = timeString.split(".")[0];
      return timePart || "N/A";
    } catch (error) {
      console.error("Error formatting time:", error);
      return "N/A";
    }
  };

  const AttendanceCard = ({ record }: { record: AttendanceRecord }) => (
    <View
      style={[
        styles.attendanceCard,
        { backgroundColor: colors.card, borderColor: colors.border },
      ]}>
      <View style={styles.cardHeader}>
        <View style={styles.cardTitleContainer}>
          <Clock size={20} color={colors.primary} />
          <Text style={[styles.cardTitle, { color: colors.text }]}>
            {record.checkOutTime ? "Full Day" : "Check-in Only"}
          </Text>
        </View>
        <View
          style={[
            styles.statusBadge,
            {
              backgroundColor: record.checkOutTime
                ? colors.success + "15"
                : colors.warning + "15",
            },
          ]}>
          <Text
            style={[
              styles.statusText,
              { color: record.checkOutTime ? colors.success : colors.warning },
            ]}>
            {record.checkOutTime ? "Complete" : "Active"}
          </Text>
        </View>
      </View>

      <View style={styles.cardContent}>
        <View style={styles.timeRow}>
          <Text style={[styles.timeLabel, { color: colors.placeholder }]}>
            Check-in:
          </Text>
          <Text style={[styles.timeValue, { color: colors.text }]}>
            {formatTime(record.checkInTime)}
          </Text>
        </View>

        {record.checkOutTime && (
          <View style={styles.timeRow}>
            <Text style={[styles.timeLabel, { color: colors.placeholder }]}>
              Check-out:
            </Text>
            <Text style={[styles.timeValue, { color: colors.text }]}>
              {formatTime(record.checkOutTime)}
            </Text>
          </View>
        )}

        <View style={styles.locationRow}>
          <MapPin size={16} color={colors.placeholder} />
          <Text style={[styles.locationText, { color: colors.placeholder }]}>
            {record.attendanceDevice?.location || "Unknown location"}
            {record.attendanceDevice?.floor &&
              ` - Floor ${record.attendanceDevice.floor}`}
          </Text>
        </View>

        {record.employee?.fullName && (
          <Text style={[styles.employeeName, { color: colors.placeholder }]}>
            {record.employee.fullName}
          </Text>
        )}
      </View>
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header with filters */}
      <View
        style={[
          styles.header,
          { backgroundColor: colors.card, borderBottomColor: colors.border },
        ]}>
        <Text style={[styles.headerTitle, { color: colors.text }]}>
          Activity Log
        </Text>

        <View style={styles.filterContainer}>
          <View style={styles.filterLabelContainer}>
            <Filter size={16} color={colors.primary} />
            <Text style={[styles.filterLabel, { color: colors.text }]}>
              Filter:
            </Text>
          </View>

          <View style={styles.filterButtons}>
            <TouchableOpacity
              style={[
                styles.filterButton,
                {
                  backgroundColor:
                    filter === "all" ? colors.primary : "transparent",
                  borderColor:
                    filter === "all" ? colors.primary : colors.border,
                },
              ]}
              onPress={() => setFilter("all")}>
              <Text
                style={[
                  styles.filterButtonText,
                  { color: filter === "all" ? "white" : colors.text },
                ]}>
                All
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.filterButton,
                {
                  backgroundColor:
                    filter === "checkin" ? colors.primary : "transparent",
                  borderColor:
                    filter === "checkin" ? colors.primary : colors.border,
                },
              ]}
              onPress={() => setFilter("checkin")}>
              <Text
                style={[
                  styles.filterButtonText,
                  { color: filter === "checkin" ? "white" : colors.text },
                ]}>
                Check-ins
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.filterButton,
                {
                  backgroundColor:
                    filter === "checkout" ? colors.primary : "transparent",
                  borderColor:
                    filter === "checkout" ? colors.primary : colors.border,
                },
              ]}
              onPress={() => setFilter("checkout")}>
              <Text
                style={[
                  styles.filterButtonText,
                  { color: filter === "checkout" ? "white" : colors.text },
                ]}>
                Complete
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>

      {/* Attendance Records */}
      <ScrollView
        style={styles.scrollContainer}
        contentContainerStyle={styles.contentContainer}>
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
            <Text style={[styles.loadingText, { color: colors.text }]}>
              Loading attendance records...
            </Text>
          </View>
        ) : filteredRecords.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Calendar
              size={48}
              color={colors.placeholder}
              style={styles.emptyIcon}
            />
            <Text style={[styles.emptyTitle, { color: colors.text }]}>
              No Records Found
            </Text>
            <Text style={[styles.emptyText, { color: colors.placeholder }]}>
              {filter === "all"
                ? "You don't have any attendance records yet."
                : `You don't have any ${
                    filter === "checkin" ? "check-in" : "completed"
                  } attendance records.`}
            </Text>
          </View>
        ) : (
          <>
            {sortedDates.map((date) => (
              <View key={date} style={styles.dateGroup}>
                <View
                  style={[
                    styles.dateHeader,
                    { backgroundColor: colors.primary + "15" },
                  ]}>
                  <Text style={[styles.dateText, { color: colors.primary }]}>
                    {date}
                  </Text>
                </View>
                {groupedRecords[date].map((record) => (
                  <AttendanceCard key={record.id} record={record} />
                ))}
              </View>
            ))}
          </>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 16,
  },
  filterContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  filterLabelContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  filterLabel: {
    fontSize: 16,
    fontWeight: "500",
  },
  filterButtons: {
    flexDirection: "row",
    gap: 8,
  },
  filterButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: "500",
  },
  scrollContainer: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingTop: 100,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingTop: 100,
  },
  emptyIcon: {
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 16,
    textAlign: "center",
    lineHeight: 24,
  },
  dateGroup: {
    marginBottom: 24,
  },
  dateHeader: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 12,
  },
  dateText: {
    fontSize: 16,
    fontWeight: "600",
  },
  attendanceCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  cardHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  cardTitleContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: "600",
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: "600",
  },
  cardContent: {
    gap: 8,
  },
  timeRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  timeLabel: {
    fontSize: 14,
    fontWeight: "500",
  },
  timeValue: {
    fontSize: 14,
    fontWeight: "600",
  },
  locationRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
    marginTop: 8,
  },
  locationText: {
    fontSize: 13,
    flex: 1,
  },
  employeeName: {
    fontSize: 12,
    fontStyle: "italic",
    marginTop: 4,
  },
});
