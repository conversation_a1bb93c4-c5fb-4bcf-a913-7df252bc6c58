import AsyncStorage from "@react-native-async-storage/async-storage";
import * as Haptics from "expo-haptics";
import { useRouter } from "expo-router";
import { BadgeCheck, Clock, LogOut, QrCode } from "lucide-react-native";
import React, { useCallback, useEffect, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  useColorScheme,
  View,
} from "react-native";
import DeviceInfo from "react-native-device-info";
import QRScanModal from "../../components/QRScanModal";
import { Colors } from "../../constants/Colors";
import {
  AttendanceRecord,
  canCheckInToday as checkCanCheckInToday,
  checkIn,
  checkOut,
  getAttendanceLogs,
  getCurrentLocation,
  isToday,
} from "../../services/attendanceService";

export default function DashboardScreen() {
  const colorScheme = useColorScheme() || "light";
  const colors = Colors?.[colorScheme] ||
    Colors?.light || {
      text: "#000",
      background: "#fff",
      primary: "#3A86FF",
      secondary: "#FF732",
      success: "#10B981",
      warning: "#F59E0B",
      error: "#EF4444",
      card: "#FFFFFF",
      border: "#E5E7EB",
      placeholder: "#9CA3AF",
    };
  const router = useRouter();

  const [user, setUser] = useState({ ecNumber: "", name: "User" });
  const [isCheckedIn, setIsCheckedIn] = useState(false);
  const [deviceId, setDeviceId] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [showScanner, setShowScanner] = useState(false);
  const [scanAction, setScanAction] = useState<"checkin" | "checkout">(
    "checkin"
  );
  const [currentRecord, setCurrentRecord] = useState<AttendanceRecord | null>(
    null
  );
  const [isCheckingStatus, setIsCheckingStatus] = useState(true);
  const [latestLog, setLatestLog] = useState<AttendanceRecord | null>(null);
  const [canCheckInToday, setCanCheckInToday] = useState(true);

  // Get current date and time
  const currentDate = new Date().toLocaleDateString("en-US", {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric",
  });

  const currentTime = new Date().toLocaleTimeString("en-US", {
    hour: "2-digit",
    minute: "2-digit",
  });

  useEffect(() => {
    const updateDeviceId = async () => {
      if (user.ecNumber) {
        const id = await DeviceInfo.getDeviceId();
        const combinedId = `${id}-${user.ecNumber}`;
        setDeviceId(combinedId);
      }
    };
    updateDeviceId();
  }, [user.ecNumber]);

  // Load user data from AsyncStorage
  useEffect(() => {
    const loadUserData = async () => {
      try {
        const storedEcNumber = await AsyncStorage.getItem("ecNumber");
        if (storedEcNumber) {
          setUser((prev) => ({ ...prev, ecNumber: storedEcNumber }));
        }
      } catch (error) {
        console.error("Failed to load user data:", error);
      }
    };

    loadUserData();
  }, []);

  // Load user data and check status
  const loadUserAndStatus = useCallback(async () => {
    try {
      setIsCheckingStatus(true);

      // Use the new service function to check status
      const { canCheckIn, isCurrentlyCheckedIn, latestRecord } =
        await checkCanCheckInToday(user.ecNumber);

      // Load all attendance logs for display purposes
      const logs = await getAttendanceLogs(user.ecNumber);
      if (logs && logs.length > 0) {
        const latest = logs[logs.length - 1];
        setLatestLog(latest);
      } else {
        setLatestLog(null);
      }

      // Set the check-in status based on the new logic
      setIsCheckedIn(isCurrentlyCheckedIn);
      setCurrentRecord(isCurrentlyCheckedIn ? latestRecord : null);
      setCanCheckInToday(canCheckIn);
    } catch (error) {
      console.error("Failed to load data:", error);
      // Set default state on error
      setIsCheckedIn(false);
      setCurrentRecord(null);
      setLatestLog(null);
      setCanCheckInToday(true);
    } finally {
      setIsCheckingStatus(false);
    }
  }, [user.ecNumber]);

  useEffect(() => {
    if (user && user.ecNumber) {
      loadUserAndStatus();
    }
  }, [user, loadUserAndStatus]);

  const handleLogout = async () => {
    Alert.alert("Logout", "Are you sure you want to logout?", [
      { text: "Cancel", style: "cancel" },
      {
        text: "Logout",
        style: "destructive",
        onPress: async () => {
          await AsyncStorage.removeItem("token");
          router.replace("/(auth)/login");
        },
      },
    ]);
  };

  const handleScan = async (data: string) => {
    try {
      setIsLoading(true);
      console.log("QR Code scanned:", data);
      console.log("Scan action:", scanAction);

      // Add haptic feedback on non-web platforms
      if (Platform.OS !== "web") {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      }

      // Get current location with user feedback
      console.log("Getting location for attendance...");
      const actualLocation = await getCurrentLocation();
      console.log("Current location:", actualLocation);

      // Validate location (warn if using fallback)
      if (actualLocation.latitude === "0" && actualLocation.longitude === "0") {
        console.warn("Using default location - GPS may not be available");
      }

      // Handle check-in/check-out based on selected action
      if (scanAction === "checkout") {
        // Real checkout API call
        const updatedRecord = await checkOut(data, deviceId, actualLocation);

        // Update local state
        setIsCheckedIn(false);
        setCurrentRecord(null);
        setLatestLog(updatedRecord);
        setShowScanner(false);

        // Refresh the status to ensure we have the latest data
        await loadUserAndStatus();

        Alert.alert(
          "Check-Out Successful",
          "You have successfully checked out.",
          [{ text: "OK" }]
        );
      } else {
        // Real checkin API call
        const newRecord = await checkIn(data, deviceId, actualLocation);

        // Update local state
        setIsCheckedIn(true);
        setCurrentRecord(newRecord);
        setLatestLog(newRecord);
        setShowScanner(false);

        // Refresh the status to ensure we have the latest data
        await loadUserAndStatus();

        Alert.alert(
          "Check-In Successful",
          "You have successfully checked in.",
          [{ text: "OK" }]
        );
      }
    } catch (error) {
      console.error("Scan error:", error);

      // Handle specific error messages from the API
      let errorMessage =
        "There was an error processing your request. Please try again.";
      if (error instanceof Error) {
        // Handle authentication errors
        if (
          error.message.includes("Authentication failed") ||
          error.message.includes("Authentication token not found")
        ) {
          Alert.alert(
            "Session Expired",
            "Your session has expired. Please login again.",
            [
              {
                text: "OK",
                onPress: async () => {
                  await AsyncStorage.removeItem("token");
                  router.replace("/(auth)/login");
                },
              },
            ]
          );
          return;
        }

        // Handle known error messages
        if (error.message.includes("Check-in already recorded")) {
          errorMessage = "You have already checked in today.";
        } else if (error.message.includes("Check-out already recorded")) {
          errorMessage = "You have already checked out today.";
        } else if (error.message.includes("Network")) {
          errorMessage =
            "Network error. Please check your internet connection and try again.";
        } else {
          errorMessage = error.message;
        }
      }

      Alert.alert("Error", errorMessage, [{ text: "OK" }]);
    } finally {
      setIsLoading(false);
      setShowScanner(false);
    }
  };

  // Format time from timestamp string
  const formatTime = (timeString: string) => {
    try {
      if (!timeString) return "N/A";

      // Split by dot to get the time part before the microseconds
      const timePart = timeString.split(".")[0];
      return timePart || "N/A";
    } catch (error) {
      console.error("Error formatting time:", error);
      return "N/A";
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    try {
      if (!dateString) return "N/A";
      return new Date(dateString).toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
        year: "numeric",
      });
    } catch (error) {
      console.error("Error formatting date:", error);
      return "N/A";
    }
  };

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: colors.background }]}
      contentContainerStyle={styles.contentContainer}>
      {/* Header with user info */}
      <View style={styles.header}>
        <View style={styles.userInfo}>
          <Text style={[styles.greeting, { color: colors.text }]}>Hello,</Text>
          <Text style={[styles.userName, { color: colors.text }]}>
            {latestLog?.employee?.fullName ||
              `ecNumber: ${user?.ecNumber}` ||
              "User"}
          </Text>
          <View
            style={[
              styles.dateTimeContainer,
              { backgroundColor: colors.primary + "15" },
            ]}>
            <Text style={[styles.dateTime, { color: colors.primary }]}>
              {currentDate} | {currentTime}
            </Text>
          </View>
        </View>

        <TouchableOpacity onPress={handleLogout} style={styles.logoutButton}>
          <LogOut size={24} color={colors.text} />
        </TouchableOpacity>
      </View>

      {/* Status Card */}
      <View
        style={[
          styles.statusCard,
          { backgroundColor: colors.card, borderColor: colors.border },
        ]}>
        <View style={styles.statusCardHeader}>
          <View style={styles.statusCardIcon}>
            <BadgeCheck size={28} color={colors.primary} />
          </View>
          <View>
            <Text style={[styles.statusCardTitle, { color: colors.text }]}>
              Today&apos;s Status
            </Text>
          </View>
        </View>

        <View
          style={[styles.statusCardDivider, { backgroundColor: colors.border }]}
        />

        {isCheckingStatus ? (
          <View style={styles.loadingStatus}>
            <ActivityIndicator color={colors.primary} />
            <Text style={[styles.loadingText, { color: colors.text }]}>
              Checking status...
            </Text>
          </View>
        ) : (
          <View style={styles.statusCardBody}>
            {isCheckedIn && currentRecord ? (
              <>
                <View
                  style={[
                    styles.statusBadge,
                    { backgroundColor: colors.success + "15" },
                  ]}>
                  <View
                    style={[
                      styles.statusIndicator,
                      { backgroundColor: colors.success },
                    ]}
                  />
                  <Text style={[styles.statusText, { color: colors.success }]}>
                    Checked In Today
                  </Text>
                </View>
                <Text style={[styles.statusDetails, { color: colors.text }]}>
                  You checked in at {formatTime(currentRecord.checkInTime)} at{" "}
                  {currentRecord.attendanceDevice?.location ||
                    "Unknown location"}
                </Text>
                {currentRecord.attendanceDevice?.floor && (
                  <Text
                    style={[
                      styles.statusDetails,
                      { color: colors.placeholder },
                    ]}>
                    Floor: {currentRecord.attendanceDevice.floor}
                  </Text>
                )}
              </>
            ) : (
              <>
                <View
                  style={[
                    styles.statusBadge,
                    { backgroundColor: colors.warning + "15" },
                  ]}>
                  <View
                    style={[
                      styles.statusIndicator,
                      { backgroundColor: colors.warning },
                    ]}
                  />
                  <Text style={[styles.statusText, { color: colors.warning }]}>
                    Not Checked In Today
                  </Text>
                </View>
                <Text style={[styles.statusDetails, { color: colors.text }]}>
                  {latestLog &&
                  !isToday(latestLog.checkInDate) &&
                  !latestLog.checkOutTime
                    ? "You can check in for today. Previous day's attendance is incomplete."
                    : latestLog &&
                      isToday(latestLog.checkInDate) &&
                      latestLog.checkOutTime
                    ? "You have already completed attendance for today."
                    : "You are currently not checked in for today"}
                </Text>
              </>
            )}
          </View>
        )}
      </View>

      {/* Latest Activity */}
      {latestLog && (
        <View
          style={[
            styles.activityCard,
            { backgroundColor: colors.card, borderColor: colors.border },
          ]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Latest Activity
          </Text>
          <View style={styles.activityItem}>
            <View style={styles.activityIcon}>
              <Clock size={20} color={colors.primary} />
            </View>
            <View style={styles.activityDetails}>
              <Text style={[styles.activityTitle, { color: colors.text }]}>
                {latestLog.checkOutTime ? "Checked Out" : "Checked In"}
              </Text>
              <Text
                style={[
                  styles.activitySubtitle,
                  { color: colors.placeholder },
                ]}>
                {formatDate(latestLog.checkInDate)} at{" "}
                {formatTime(latestLog.checkOutTime || latestLog.checkInTime)}
              </Text>
              <Text
                style={[
                  styles.activityLocation,
                  { color: colors.placeholder },
                ]}>
                📍 {latestLog.attendanceDevice?.location || "Unknown location"}
                {latestLog.attendanceDevice?.floor &&
                  ` - Floor ${latestLog.attendanceDevice.floor}`}
              </Text>
            </View>
          </View>
        </View>
      )}

      {/* Check In/Out Buttons */}
      <View style={styles.actionContainer}>
        <TouchableOpacity
          style={[
            styles.checkInButton,
            { backgroundColor: colors.success },
            (!canCheckInToday || isLoading) && styles.disabledButton,
          ]}
          onPress={() => {
            setScanAction("checkin");
            setShowScanner(true);
          }}
          disabled={isLoading || !canCheckInToday}>
          {isLoading && scanAction === "checkin" ? (
            <ActivityIndicator size={24} color="#fff" />
          ) : (
            <QrCode size={24} color="#fff" />
          )}
          <Text style={styles.buttonText}>Check In</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.checkOutButton,
            { backgroundColor: colors.error },
            !isCheckedIn && styles.disabledButton,
          ]}
          onPress={() => {
            setScanAction("checkout");
            setShowScanner(true);
          }}
          disabled={isLoading || !isCheckedIn}>
          {isLoading && scanAction === "checkout" ? (
            <ActivityIndicator size={24} color="#fff" />
          ) : (
            <QrCode size={24} color="#fff" />
          )}
          <Text style={styles.buttonText}>Check Out</Text>
        </TouchableOpacity>
      </View>

      <QRScanModal
        isVisible={showScanner}
        onClose={() => setShowScanner(false)}
        onScan={handleScan}
        isLoading={isLoading}
      />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 32,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 24,
  },
  userInfo: {
    flex: 1,
  },
  greeting: {
    fontSize: 16,
  },
  userName: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 8,
  },
  dateTimeContainer: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    alignSelf: "flex-start",
  },
  dateTime: {
    fontSize: 12,
    fontWeight: "500",
  },
  logoutButton: {
    padding: 8,
  },
  statusCard: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 24,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
  },
  statusCardHeader: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  statusCardIcon: {
    padding: 8,
  },
  statusCardTitle: {
    fontSize: 18,
    fontWeight: "bold",
  },
  statusCardDivider: {
    height: 1,
    marginVertical: 16,
  },
  statusCardBody: {
    gap: 8,
  },
  loadingStatus: {
    alignItems: "center",
    padding: 8,
    gap: 8,
  },
  loadingText: {
    fontSize: 14,
  },
  statusBadge: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    alignSelf: "flex-start",
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  statusText: {
    fontSize: 14,
    fontWeight: "500",
  },
  statusDetails: {
    fontSize: 14,
    lineHeight: 20,
  },
  activityCard: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 24,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 16,
  },
  activityItem: {
    flexDirection: "row",
    alignItems: "flex-start",
    gap: 12,
  },
  activityIcon: {
    padding: 8,
  },
  activityDetails: {
    flex: 1,
  },
  activityTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  activitySubtitle: {
    fontSize: 14,
    marginBottom: 2,
  },
  activityLocation: {
    fontSize: 13,
  },
  actionContainer: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-around",
    gap: 16,
  },
  checkInButton: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    flex: 1,
    justifyContent: "center",
  },
  checkOutButton: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    flex: 1,
    justifyContent: "center",
  },
  disabledButton: {
    opacity: 0.5,
  },
  buttonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "bold",
  },
});
