/**
 * Below are the colors that are used in the app. The colors are defined in the light and dark mode.
 * There are many other ways to style your app. For example, [Nativewind](https://www.nativewind.dev/), [Tamagui](https://tamagui.dev/), [unistyles](https://reactnativeunistyles.vercel.app), etc.
 */

const tintColorLight = "#3A86FF";
const tintColorDark = "#fff";

const Colors = {
  light: {
    text: "#11181C",
    background: "#fff",
    tint: tintColorLight,
    icon: "#687076",
    tabIconDefault: "#687076",
    tabIconSelected: tintColorLight,
    primary: "#3A86FF",
    secondary: "#FF732",
    success: "#10B981",
    warning: "#F59E0B",
    error: "#EF4444",
    card: "#FFFFFF",
    border: "#E5E7EB",
    placeholder: "#9CA3AF",
  },
  dark: {
    text: "#ECEDEE",
    background: "#151718",
    tint: tintColorDark,
    icon: "#9BA1A6",
    tabIconDefault: "#9BA1A6",
    tabIconSelected: tintColorDark,
    primary: "#3A86FF",
    secondary: "#FF732",
    success: "#10B981",
    warning: "#F59E0B",
    error: "#EF4444",
    card: "#1F2937",
    border: "#374151",
    placeholder: "#6B7280",
  },
};

export default Colors;
export { Colors };
