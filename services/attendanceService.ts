import AsyncStorage from "@react-native-async-storage/async-storage";
import * as Location from "expo-location";
import { BASE_URL } from "../utils/config";

export interface AttendanceRecord {
  id: string;
  employee: {
    id: string;
    ecNumber: string;
    fullName: string;
    devices: string[];
    role: string;
    password?: string | null;
    authorities?: {
      authority: string;
    }[];
    username?: string;
    enabled?: boolean;
    accountNonExpired?: boolean;
    accountNonLocked?: boolean;
    credentialsNonExpired?: boolean;
  };
  attendanceDevice: {
    id: string;
    deviceId: string;
    location: string;
    floor: string;
  };
  checkInDate: string;
  checkInTime: string;
  checkOutTime: string | null;
  actualCheckInLocation?: {
    longitude: string;
    latitude: string;
  } | null;
  actualLocation?: any | null;
  attendanceCode: string;
}

export interface AttendanceResponse {
  success: boolean;
  message: string;
  body: AttendanceRecord[] | AttendanceRecord | null;
}

export interface CheckInRequest {
  attendanceCode: string;
  deviceId: string;
  actualLocation: {
    latitude: string;
    longitude: string;
  };
}

export interface CheckOutRequest {
  attendanceCode: string;
  deviceId: string;
  actualLocation: {
    latitude: string;
    longitude: string;
  };
}

export const getAttendanceLogs = async (
  ecNumber: string
): Promise<AttendanceRecord[]> => {
  try {
    const token = await AsyncStorage.getItem("token");
    console.log(ecNumber);
    const response = await fetch(
      `${BASE_URL}/api/attendance-log?ecNumber=${ecNumber}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: AttendanceResponse = await response.json();

    if (data && data.success && Array.isArray(data.body)) {
      return data.body;
    } else {
      console.warn("Invalid response format:", data);
      return [];
    }
  } catch (error) {
    console.error("Failed to fetch attendance logs:", error);
    return []; // Return empty array instead of throwing
  }
};

export const getCurrentCheckInStatus = async (
  ecNumber: string
): Promise<AttendanceRecord | null> => {
  try {
    const logs = await getAttendanceLogs(ecNumber);

    // Find the most recent log
    if (logs.length > 0) {
      const latestLog = logs[logs.length - 1];

      // If the latest log doesn't have a checkout time, user is checked in
      if (!latestLog.checkOutTime) {
        return latestLog;
      }
    }

    return null;
  } catch (error) {
    console.error("Failed to get current check-in status:", error);
    throw error;
  }
};

export const checkIn = async (
  attendanceCode: string,
  deviceId: string,
  actualLocation: { latitude: string; longitude: string }
): Promise<AttendanceRecord> => {
  try {
    const token = await AsyncStorage.getItem("token");
    if (!token) {
      throw new Error("Authentication token not found. Please login again.");
    }
    console.log(attendanceCode, deviceId, actualLocation);
    const response = await fetch(`${BASE_URL}/api/attendance-log/check-in`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        attendanceCode,
        deviceId,
        actualLocation,
      }),
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error("Authentication failed. Please login again.");
      }
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: AttendanceResponse = await response.json();

    if (data.success && data.body) {
      return data.body as AttendanceRecord;
    } else {
      throw new Error(data.message || "Check-in failed");
    }
  } catch (error) {
    console.error("Failed to check in:", error);
    throw error;
  }
};

export const checkOut = async (
  attendanceCode: string,
  deviceId: string,
  actualLocation: { latitude: string; longitude: string }
): Promise<AttendanceRecord> => {
  try {
    const token = await AsyncStorage.getItem("token");
    if (!token) {
      throw new Error("Authentication token not found. Please login again.");
    }

    const response = await fetch(`${BASE_URL}/api/attendance-log/check-out`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        attendanceCode,
        deviceId,
        actualLocation,
      }),
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error("Authentication failed. Please login again.");
      }
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: AttendanceResponse = await response.json();

    if (data.success && data.body) {
      return data.body as AttendanceRecord;
    } else {
      throw new Error(data.message || "Check-out failed");
    }
  } catch (error) {
    console.error("Failed to check out:", error);
    throw error;
  }
};

export const getDeviceId = async (ecNumber?: string): Promise<string> => {
  try {
    const DeviceInfo = await import("react-native-device-info");
    const deviceId = await DeviceInfo.default.getDeviceId();

    // Combine deviceId with ecNumber if provided
    const combinedDeviceId = ecNumber ? `${deviceId}-${ecNumber}` : deviceId;

    console.log("Device ID retrieved:", combinedDeviceId);
    return combinedDeviceId;
  } catch (error) {
    console.error("Failed to get device ID:", error);
    // Fallback to a default device ID with ecNumber if provided
    const fallbackId = ecNumber
      ? `default-device-id-${ecNumber}`
      : "default-device-id";
    console.warn("Using fallback device ID:", fallbackId);
    return fallbackId;
  }
};

export const getCurrentLocation = async (): Promise<{
  latitude: string;
  longitude: string;
}> => {
  try {
    console.log("Requesting location permissions...");
    const { status } = await Location.requestForegroundPermissionsAsync();

    if (status !== "granted") {
      console.warn("Location permission not granted, using default location");
      return {
        latitude: "0",
        longitude: "0",
      };
    }

    console.log("Getting current location...");
    const location = await Location.getCurrentPositionAsync({
      accuracy: Location.Accuracy.Balanced,
    });

    const actualLocation = {
      latitude: location.coords.latitude.toString(),
      longitude: location.coords.longitude.toString(),
    };

    console.log("Location retrieved successfully:", actualLocation);
    return actualLocation;
  } catch (error) {
    console.error("Failed to get location:", error);

    // Try to get last known location as fallback
    try {
      console.log("Attempting to get last known location...");
      const lastLocation = await Location.getLastKnownPositionAsync({});

      if (lastLocation) {
        const fallbackLocation = {
          latitude: lastLocation.coords.latitude.toString(),
          longitude: lastLocation.coords.longitude.toString(),
        };
        console.log("Using last known location:", fallbackLocation);
        return fallbackLocation;
      }
    } catch (lastLocationError) {
      console.error("Failed to get last known location:", lastLocationError);
    }

    // Final fallback to default location
    console.warn("Using default coordinates (0,0)");
    return {
      latitude: "0",
      longitude: "0",
    };
  }
};

// Utility function to check if a date is today
export const isToday = (dateString: string): boolean => {
  try {
    const logDate = new Date(dateString);
    const today = new Date();
    return (
      logDate.getDate() === today.getDate() &&
      logDate.getMonth() === today.getMonth() &&
      logDate.getFullYear() === today.getFullYear()
    );
  } catch (error) {
    console.error("Error comparing dates:", error);
    return false;
  }
};

// Check if user can check in for today
export const canCheckInToday = async (
  ecNumber: string
): Promise<{
  canCheckIn: boolean;
  isCurrentlyCheckedIn: boolean;
  latestRecord: AttendanceRecord | null;
}> => {
  try {
    const logs = await getAttendanceLogs(ecNumber);

    if (logs.length === 0) {
      return {
        canCheckIn: true,
        isCurrentlyCheckedIn: false,
        latestRecord: null,
      };
    }

    const latestLog = logs[logs.length - 1];

    // If latest log is from today and user hasn't checked out, they're currently checked in
    if (isToday(latestLog.checkInDate) && !latestLog.checkOutTime) {
      return {
        canCheckIn: false,
        isCurrentlyCheckedIn: true,
        latestRecord: latestLog,
      };
    }

    // If latest log is from today and user has checked out, they can't check in again today
    if (isToday(latestLog.checkInDate) && latestLog.checkOutTime) {
      return {
        canCheckIn: false,
        isCurrentlyCheckedIn: false,
        latestRecord: latestLog,
      };
    }

    // If latest log is from a previous day (regardless of checkout status), user can check in today
    return {
      canCheckIn: true,
      isCurrentlyCheckedIn: false,
      latestRecord: latestLog,
    };
  } catch (error) {
    console.error("Failed to check if user can check in today:", error);
    return {
      canCheckIn: true,
      isCurrentlyCheckedIn: false,
      latestRecord: null,
    };
  }
};
